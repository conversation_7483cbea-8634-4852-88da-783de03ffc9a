import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../utils/card_view_menu.dart';
import '../../utils/header_banner.dart';
import '../../widgets/bidder_widgets.dart';
import '../../widgets/login_dialog.dart';

class SellerFragment extends StatefulWidget {

  @override
  State<SellerFragment> createState() => _SellerFragmentState();
}

class _SellerFragmentState extends State<SellerFragment> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });


    setState(() {
      _isLoading = false;
    });
  }


  void _navigateToWebView(String url, String title) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewScreen(url: url, title: title),
      ),
    );
  }

  void _showLoginDialog() {
    context.showLoginDialog(
      onLoginSuccess: () {
        // Refresh data setelah login berhasil
        _loadData();
      },
    );
  }

  void _showNotSellerDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Akses Ditolak'),
        content: const Text('Menu ini khusus untuk Seller. Silakan daftar sebagai Seller terlebih dahulu.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _contactSellerSupport();
            },
            child: const Text('Daftar Seller'),
          ),
        ],
      ),
    );
  }

  Future<void> _contactSellerSupport() async {
    const supportNumber = "6282231097260"; // Ganti dengan nomor bantuan yang sebenarnya
    final url = "https://api.whatsapp.com/send/?phone=$supportNumber&text=Saya ingin mendaftar sebagai seller, mohon informasinya.&type=phone_number&app_absent=0";
    final uri = Uri.parse(url);

    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showErrorDialog('WhatsApp tidak terinstall di perangkat ini.');
      }
    } catch (e) {
      _showErrorDialog('Gagal membuka WhatsApp: $e');
    }
  }

  Future<void> _handleSellerMenuTap(String menuName) async {
    final prefs = await SharedPreferences.getInstance();
    String? session = prefs.getString("session");
    if (session!.isEmpty) {
      _showLoginDialog();
      return;
    }

    // Navigate to specific seller screens
    switch (menuName) {
      case 'Dashboard Seller':
        Navigator.pushNamed(context, '/seller/dashboard');
        break;
      case 'Invoice Fee BOT':
        Navigator.pushNamed(context, '/seller/invoice');
        break;
      case 'Poin':
        _navigateToWebView('/seller/poin', 'Poin Seller');
        break;
      case 'Lelang':
        Navigator.pushNamed(context, '/seller/lelang');
        break;
      case 'Edit Ikan':
        Navigator.pushNamed(context, '/seller/edit-ikan');
        break;
      case 'Ikan Terjual':
        Navigator.pushNamed(context, '/seller/ikan-terjual');
        break;
      case 'Member Aktif':
        Navigator.pushNamed(context, '/seller/member-aktif');
        break;
      case 'Most Auction':
        _navigateToWebView('/seller/most-auction', 'Most Auction');
        break;
      case 'Edit Profil Seller':
        Navigator.pushNamed(context, '/seller/profile');
        break;
      case 'Ads Analytic':
        Navigator.pushNamed(context, '/seller/ads-analytics');
        break;
      case 'Rekening Penjamin':
        _navigateToWebView('/seller/rekening-penjamin', 'Rekening Penjamin');
        break;
      case 'Seller Bad Debt':
        _navigateToWebView('/seller/baddebt', 'Seller Bad Debt');
        break;
      default:
        _showErrorDialog('Fitur "$menuName" belum tersedia.');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }


  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Warning text
        Container(
          width: double.infinity,
          color: Colors.red,
          padding: const EdgeInsets.all(8),
          child: const Text(
            "Hati-hati penipuan! Pastikan Anda bertransaksi melalui rekening penjamin resmi.",
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),

        // Main content
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      // Banner slider

                      HeaderBanner(
                        imageNumber: 'gambar4.png',
                        text: 'Terintegrasi Real-Time antara WhatsApp dan Telegram, serta terpublis di Official Web 😎',
                        height: 160,
                      ),

                      // Cara Jadi Seller card
                      if (true)
                        Transform.translate(
                          offset: const Offset(0, -30),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Card(
                              margin: const EdgeInsets.only(bottom: 30),
                              elevation: 4,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: InkWell(
                                onTap: _contactSellerSupport,
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    children: [
                                      // New icon
                                      Image.asset(
                                        'assets/images/logo_new.png',
                                        width: 60,
                                        height: 60,
                                        errorBuilder: (_, __, ___) => const Icon(
                                          Icons.new_releases,
                                          size: 60,
                                          color: Colors.orange,
                                        ),
                                      ),

                                      // Click icon
                                      Image.asset(
                                        'assets/images/click.gif',
                                        width: 80,
                                        height: 80,
                                        errorBuilder: (_, __, ___) => const Icon(
                                          Icons.touch_app,
                                          size: 80,
                                          color: Colors.blue,
                                        ),
                                      ),

                                      // Content
                                      Expanded(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            const Icon(
                                              Icons.store,
                                              size: 38,
                                              color: Color(0xFFFFD700), // Gold color
                                            ),
                                            const SizedBox(height: 8),
                                            const Text(
                                              "Cara Jadi Seller",
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                fontSize: 16,
                                                color: Color(0xFF424242), // grey_80
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                      // Grid categories
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Column(
                          children: [
                            // Row 1: Dashboard, Invoice, Point
                            Row(
                              children: [
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.assessment_rounded,
                                    text: "Dashboard Seller",
                                    onTap: () => _handleSellerMenuTap('Dashboard Seller'),
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.receipt_long,
                                    text: "Invoice Fee BOT",
                                    onTap: () => _handleSellerMenuTap('Invoice Fee BOT'),
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.stars,
                                    text: "Poin",
                                    onTap: () => _handleSellerMenuTap('Poin'),
                                  ),
                                ),
                              ],
                            ),

                            // Row 2: Lelang, Edit Ikan, Ikan Terjual
                            Row(
                              children: [
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.play_circle,
                                    text: "Lelang",
                                    onTap: () => _handleSellerMenuTap('Lelang'),
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.edit,
                                    text: "Edit Ikan",
                                    onTap: () => _handleSellerMenuTap('Edit Ikan'),
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.shopping_cart,
                                    text: "Ikan Terjual",
                                    onTap: () => _handleSellerMenuTap('Ikan Terjual'),
                                  ),
                                ),
                              ],
                            ),

                            // Row 3: Member Aktif, Most Auction, Edit Profil
                            Row(
                              children: [
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.person_add,
                                    text: "Member Aktif",
                                    onTap: () => _handleSellerMenuTap('Member Aktif'),
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.monetization_on,
                                    text: "Most Auction",
                                    onTap: () => _handleSellerMenuTap('Most Auction'),
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.manage_accounts,
                                    text: "Edit Profil Seller",
                                    onTap: () => _handleSellerMenuTap('Edit Profil Seller'),
                                  ),
                                ),
                              ],
                            ),

                            // Row 4: Ads Analytics, Rekening Penjamin, Bad Debt
                            Row(
                              children: [
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.desktop_mac,
                                    text: "Ads Analytic",
                                    onTap: () => _handleSellerMenuTap('Ads Analytic'),
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.credit_card,
                                    text: "Rekening Penjamin",
                                    onTap: () => _handleSellerMenuTap('Rekening Penjamin'),
                                  ),
                                ),
                                Expanded(
                                  child: CardViewMenuItem(
                                    icon: Icons.store_mall_directory_outlined,
                                    text: "Seller Bad Debt",
                                    onTap: () => _handleSellerMenuTap('Seller Bad Debt'),
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 24),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
        ),
      ],
    );
  }
}
